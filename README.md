# Pharmacy Inventory Agent (rxinv-agent)

## Introduction
This project provides an AI-powered agent for analyzing inventory data. The agent uses Databricks, MLflow, and Streamlit to enable natural language querying of pharmacy datasets.

## Environment Setup

### Prerequisites
- Conda (Miniconda or Anaconda)
- Python 3.12
- Access to Databricks cluster (ID: 0710-201525-xdzms0rp)

### Creating Conda Environment
```bash
conda create -n rxinv-agent python=3.12 ipython
conda activate rxinv-agent