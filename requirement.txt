aiohappyeyeballs==2.6.1
aiohttp==3.12.14
aiohttp-retry==2.9.1
aiosignal==1.4.0
alembic==1.16.4
altair==5.5.0
annotated-types==0.7.0
anyio==4.9.0
attrs==25.3.0
azure-core==1.35.0
azure-storage-blob==12.26.0
azure-storage-file-datalake==12.21.0
blinker==1.9.0
boto3==1.39.8
botocore==1.39.8
cachetools==5.5.2
certifi==2025.7.14
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
cloudpickle==3.1.1
contourpy==1.3.2
cryptography==45.0.5
cycler==0.12.1
databricks-agents==1.2.0
databricks-ai-bridge==0.6.0
databricks-connect==16.3.5
databricks-langchain==0.6.0
databricks-sdk==0.59.0
databricks-vectorsearch==0.57
dataclasses-json==0.6.7
deprecation==2.1.0
distro==1.9.0
docker==7.1.0
fastapi==0.116.1
flask==3.1.1
fonttools==4.59.0
frozenlist==1.7.0
gitdb==4.0.12
gitpython==3.1.44
google-api-core==2.25.1
google-auth==2.40.3
google-cloud-core==2.4.3
google-cloud-storage==3.2.0
google-crc32c==1.7.1
google-resumable-media==2.7.2
googleapis-common-protos==1.70.0
graphene==3.4.3
graphql-core==3.2.6
graphql-relay==3.2.0
grpcio-status==1.71.2
gunicorn==23.0.0
h11==0.16.0
httpcore==1.0.9
httpx==0.28.1
httpx-sse==0.4.1
idna==3.10
importlib-metadata==8.7.0
isodate==0.7.2
itsdangerous==2.2.0
jinja2==3.1.6
jiter==0.10.0
joblib==1.5.1
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
kiwisolver==1.4.8
langchain==0.3.26
langchain-community==0.3.27
langchain-core==0.3.69
langchain-openai==0.3.28
langchain-text-splitters==0.3.8
langgraph==0.3.4
langgraph-checkpoint==2.1.1
langgraph-prebuilt==0.1.9
langgraph-sdk==0.1.73
langsmith==0.4.7
mako==1.3.10
markupsafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.3
mlflow==3.1.1
mlflow-skinny==3.1.1
multidict==6.6.3
mypy-extensions==1.1.0
narwhals==1.47.1
nest-asyncio==1.6.0
numpy==1.26.4
openai==1.97.0
opentelemetry-api==1.35.0
opentelemetry-sdk==1.35.0
opentelemetry-semantic-conventions==0.56b0
orjson==3.11.0
packaging==25.0
pandas==2.3.1
pillow==11.3.0
promptcache==0.3.2
proto-plus==1.26.1
protobuf==5.29.5
py4j==********
pyarrow==20.0.0
pyasn1==0.6.1
pyasn1-modules==0.4.2
pycparser==2.22
pydantic==2.11.7
pydantic-settings==2.10.1
pydantic-core==2.33.2
pydeck==0.9.1
pyparsing==3.2.3
python-dateutil==2.9.0
python-dotenv==1.1.1
pytz==2025.2
pyyaml==6.0.2
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
requests-toolbelt==1.0.0
rpds-py==0.26.0
rsa==4.9.1
s3transfer==0.13.0
scikit-learn==1.7.1
scipy==1.16.0
setuptools==80.9.0
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
sqlalchemy==2.0.41
sqlparse==0.5.3
starlette==0.47.1
streamlit==1.46.0
tabulate==0.9.0
tenacity==9.1.2
threadpoolctl==3.6.0
tiktoken==0.9.0
toml==0.10.2
tornado==6.5.1
tqdm==4.67.1
typing-extensions==4.14.1
typing-inspect==0.9.0
typing-inspection==0.4.1
tzdata==2025.2
unitycatalog-ai==0.3.1
unitycatalog-client==0.3.0
unitycatalog-langchain==0.2.0
urllib3==2.5.0
uv==0.8.0
uvicorn==0.35.0
werkzeug==3.1.3
whenever==0.7.3
yarl==1.20.1
zipp==3.23.0
zstandard==0.23.0