import streamlit as st
from mlflow.types.agent import ChatContext
from agent import AGENT
from datetime import datetime

# Set up the chatbot context
context = ChatContext(
    user_id="default",
    session_id="sess",
    conversation_id='cid'
)

# Initialize system prompt
system_prompt = "You are a helpful agent who works for  and can use the provided tools to generate queries against 's pharmacy data."

# Initialize chat history
if "history" not in st.session_state:
    st.session_state.history = [
        {"role": "system", "content": system_prompt}
    ]

def ask_question(agent, question, context=None, history=None):
    """Ask a question to the agent and get the response"""
    if history is None:
        history = []
    
    # Create user message
    this_turn = [
        {"role": "user", "content": question},
    ]
    
    # Combine history with this turn
    all_messages = history + this_turn
    
    # Get agent response
    response = agent.predict(
        messages=all_messages,
        context=context,
        custom_inputs={"history": history}
    )
    
    # Add messages to history
    for msg in response.messages:
        history.append(msg)
    
    return response

def handle_feedback(run_id=None):
    """Handle user feedback submission"""
    try:
        if run_id is not None and "feedback" in st.session_state:
            feedback = st.session_state.feedback
            with open("./wally_feedback_logs/wally_feedback.txt", "a") as f:
                f.write(f"{datetime.now().isoformat()},{run_id},{feedback}\n")
    except Exception as e:
        st.error(f"Error saving feedback: {e}")

# Streamlit app setup
st.set_page_config(page_title="Wally", page_icon="💊")
st.title("Wally Greensdata")

# Initialize chat messages
if "messages" not in st.session_state:
    st.session_state.messages = [
        {"role": "assistant", "content": "Hi, I'm Wally! How can I help you with  pharmacy data today?"}
    ]

# Display chat messages
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# Handle user input
if prompt := st.chat_input("What would you like to know?"):
    # Display user message
    with st.chat_message("user"):
        st.markdown(prompt)
    
    # Add user message to history
    st.session_state.messages.append({"role": "user", "content": prompt})
    
    # Get agent response
    with st.spinner("Analyzing your question..."):
        response = ask_question(
            AGENT, 
            prompt, 
            context=context, 
            history=st.session_state.history
        )
        
        # Get final response content
        final_response = response.messages[-1].model_dump().get("content", "<No content>")
        final_response_id = response.messages[-1].model_dump().get("id", "unknown")
        
        # Display assistant response
        with st.chat_message("assistant"):
            st.markdown(final_response)
        
        # Add feedback component
        st.feedback(
            feedback_type="thumbs",
            key="feedback",
            on_submit=handle_feedback,
            kwargs={'run_id': final_response_id}
        )
        
        # Add assistant response to messages
        st.session_state.messages.append({"role": "assistant", "content": final_response})
        
        # Update history
        for msg in response.messages:
            st.session_state.history.append(msg)