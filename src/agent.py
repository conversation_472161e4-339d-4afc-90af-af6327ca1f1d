from typing import Any, Generator, Optional, Sequence, Union, List, Dict
import mlflow
from databricks.langchain import (
    ChatDatabricks,
    DatabricksFunctionClient,
    set_uc_function_client,
)
from langchain_core.tools import tool
from langchain_core.language_models import LanguageModelLike
from langchain_core.runnables import RunnableConfig, RunnableLambda
from langchain_core.tools import BaseTool
from langgraph.graph import END, StateGraph
from langgraph.graph.graph import CompiledGraph
from langgraph.graph.state import CompiledStateGraph
from langgraph.prebuilt.tool_node import ToolNode
from mlflow.langchain.chat_agent_langgraph import ChatAgentState, ChatAgentToolNode
from mlflow_pyfunc import ChatAgent
from mlflow.types.agent import (
    ChatAgentChunk,
    ChatAgentMessage,
    ChatAgentResponse,
    ChatContext,
)
from databricks.connect import DatabricksSession
from pyspark.dbutils import DBUtils
import pyspark.sql.functions as F
from pyspark.sql.window import Window
import os

def get_spark_session():
    spark = DatabricksSession.builder.remote(
        host=os.environ.get("DATABRICKS_HOST"),
        token=os.environ.get("DATABRICKS_TOKEN"),
        cluster_id="023654"
    ).getOrCreate()
    return spark

def get_dbutils(spark):
    dbutils = DBUtils(spark)
    return dbutils

### Define tools for your agent ###
@tool
def multiply(a: int, b: int) -> int:
    """Multiply two numbers."""
    return a * b

@tool
def get_product_name_by_ndc(ndc: str) -> str:
    """Returns the product name for a given ndc"""
    spark = get_spark_session()
    return spark.read.load("abfss:/m") \
        .select("product_full_name") \
        .take(1)[0]

@tool
def get_product_name_by_drug_id(drug_id: str) -> str:
    """Returns the product name for a given drug id"""
    spark = get_spark_session()
    return spark.read.load("abfss:/m") \
        .filter(F.col("drug_id") == drug_id) \
        .select("product_full_name") \
        .take(1)[0]

@tool
def get_drug_id_by_name_match(drug_name: str) -> List[str]:
    """Returns the drug id's for drugs which match the given product name."""
    spark = get_spark_session()
    return [row[0] for row in spark.read.load("abfss:/m") \
        .filter(F.col("product_full_name").like(f"%{drug_name.upper()}%")) \
        .select("drug_id") \
        .collect()]

@tool
def do_product_lookup(lookup_key: str, lookup_values: List[str], return_cols: List[str]) -> List[Dict]:
    """Does a generic lookup on drug products for the given lookup values."""
    spark = get_spark_session()
    prd = spark.read.load("abfss:/m")
    filtered = prd.filter(F.col(lookup_key).isin(lookup_values))
    cols_we_want = filtered.select(lookup_key, *return_cols)
    return [v.asDict() for v in cols_we_want.collect()]

@tool
def get_sales_by_drug(start_date: str, end_date: str, n: int = 500) -> List[Dict]:
    """Returns sales data for top n selling drugs."""
    spark = get_spark_session()
    df = spark.read.load("abfss:/m") \
        .filter("year IN (2025) AND month >= 5") \
        .withColumn("rn", F.row_number().over(Window.partitionBy("rx_nbr", "store_nbr", "fill_nbr_dispensed").orderBy(F.desc("fill_nbr")))) \
        .filter(F.col("fill_sold_dttm").isNotNull()) \
        .filter((F.col("fill_sold_dttm") >= start_date) & (F.col("fill_sold_dttm") < end_date)) \
        .groupby("drug_id") \
        .agg(
            F.sum("fill_qty_dispensed").alias("quantity_dispensed"),
            F.sum("fill_wac_cost_amt").alias("dollar_value_dispensed"),
            F.countDistinct(F.concat("rx_nbr", "store_nbr")).alias("num_scripts")
        ) \
        .sort(F.desc("dollar_value_dispensed"))
    return [v.asDict() for v in df.take(n)]

@tool
def query_data(query: str) -> Union[str, List[Dict]]:
    """Executes a SQL query using Spark.
    Executes a SQL query on any accessible Spark table. Table names follow the format:
    `[data_format].[absolute_path_to_table]` (e.g., 'delta.abfss://cur@...').

    
    """
    spark = get_spark_session()
    try:
        result = spark.sql(query)
        return [row.asDict() for row in result.collect()]
    except Exception as e:
        return f"Query execution failed: {str(e)}"
    
@tool
def query_zot(query: str) -> List[Dict]:
    try:
        # Actual query execution would go here
        return [{"result": "sample data"}]
    except Exception as e:
        return [{"error": f"There was an error: {str(e)}"}]

@tool
def get_rxa_config(config_name: str) -> Dict:
    """
    Returns the full path of the configuration item we want to use.
    Allowed configurations:
    - min_c_targets
        • Description: minimum covid vaccine targets
        • Columns:
            - _c0: store number (str_nbr)
            - _c1: pln
            - _c2: min_target
    """
    config_paths = {
        'min_c_targets': {
            'base_path': 'abfss:/m_targets',
            'format': 'csv'
        }
    }
    
    spark = get_spark_session()
    dbutils = get_dbutils(spark)
    table_path = config_paths[config_name]
    file_list = dbutils.fs.ls(table_path['base_path'])
    latest_file = sorted(file_list, key=lambda x: x.modificationTime)[-1]
    return {
        'file_path': latest_file.path,
        'table_format': table_path['format']
    }

@tool
def get_mfc_locations() -> List[Dict]:
    """
    Returns location numbers and names of microfulfillment centers (MFCs)
    """
    return [
        {'location_name': "A", 'store_nbr': 4},
        {'location_name': "B", 'store_nbr': 1},
    ]

# Tool definitions from previous implementation
@tool
def multiply(a: int, b: int) -> int:
    """Multiply two numbers."""
    return a * b

@tool
def get_product_name_by_ndc(ndc: str) -> str:
    """Returns product name for given NDC"""
    pass

# Additional tool definitions would go here...

# Define tools
tools = [
    query_zot,
    get_rxa_config,
    get_mfc_locations,
    multiply,
    get_product_name_by_ndc
    # Other tools from previous implementation
]

# MLflow setup
mlflow.langchain.autolog()
client = DatabricksFunctionClient()
set_uc_function_client(client)

# Define LLM endpoint
LLM_ENDPOINT_NAME = "databricks-claude-3-7-sonnet"
llm = ChatDatabricks(endpoint=LLM_ENDPOINT_NAME)

# System prompt
system_prompt = (
    "You are a helpful assistant specialized in pharmacy operations and inventory management. "
    "Use the available tools to answer questions about drug products, sales data, and inventory."
)

# Define agent logic
def create_tool_calling_agent(
    model: LanguageModelLike,
    tools: Union[Sequence[BaseTool], ToolNode],
    system_prompt: Optional[str] = None,
) -> CompiledGraph:
    model = model.bind_tools(tools)
    
    def should_continue(state: ChatAgentState):
        messages = state["messages"]
        last_message = messages[-1]
        
        if last_message.get("tool_calls"):
            return "continue"
        else:
            return "end"
    
    if system_prompt:
        preprocessor = RunnableLambda(
            lambda state: [{"role": "system", "content": system_prompt}] + state["messages"]
        )
    else:
        preprocessor = RunnableLambda(lambda state: state["messages"])
    
    model_runnable = preprocessor | model
    
    def call_model(state: ChatAgentState, config: RunnableConfig):
        response = model_runnable.invoke(state, config)
        return {"messages": [response]}
    
    workflow = StateGraph(ChatAgentState)
    workflow.add_node("agent", RunnableLambda(call_model))
    workflow.add_node("tools", ChatAgentToolNode(tools))
    
    workflow.set_entry_point("agent")
    workflow.add_conditional_edges(
        "agent",
        should_continue,
        {
            "continue": "tools",
            "end": END
        }
    )
    workflow.add_edge("tools", "agent")
    
    return workflow.compile()

class LangGraphChatAgent(ChatAgent):
    def __init__(self, agent: CompiledStateGraph):
        self.agent = agent
    
    def predict(
        self,
        messages: List[ChatAgentMessage],
        context: Optional[ChatContext] = None,
        custom_inputs: Optional[Dict[str, Any]] = None,
    ) -> ChatAgentResponse:
        custom_inputs = custom_inputs or {}
        history = custom_inputs.get("history", [])
        
        # Merge history with current input
        full_conversation = history + messages
        request = {"messages": self._convert_messages_to_dict(full_conversation)}
        
        response_messages = []
        for event in self.agent.stream(request, stream_mode="updates"):
            for node_data in event.values():
                response_messages.extend(
                    ChatAgentMessage(**msg) for msg in node_data.get("messages", [])
                )
        
        # Update history for next turn
        if "history" in custom_inputs:
            custom_inputs["history"].extend(messages)
            custom_inputs["history"].extend(response_messages)
        
        return ChatAgentResponse(messages=response_messages)
    
    def predict_stream(
        self,
        messages: List[ChatAgentMessage],
        context: Optional[ChatContext] = None,
        custom_inputs: Optional[Dict[str, Any]] = None,
    ) -> Generator[ChatAgentChunk, None, None]:
        request = {"messages": self._convert_messages_to_dict(messages)}
        for event in self.agent.stream(request, stream_mode="updates"):
            for node_data in event.values():
                for msg in node_data.get("messages", []):
                    yield ChatAgentChunk(**{"delta": msg})

# Create and register the agent
agent = create_tool_calling_agent(llm, tools, system_prompt)
AGENT = LangGraphChatAgent(agent)
mlflow.models.set_model(AGENT)    