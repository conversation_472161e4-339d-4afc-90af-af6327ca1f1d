# Byte-compiled / optimized files
__pycache__/
*.py[cod]
*.py.class

# C extensions
*.so

# Distribution / packaging
build/
develop-eggs/
dist/
downloads/
eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask
instance/
.webassets-cache

# Scrapy
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints
profile_default/
ipython_config.py

# pipenv
#   According to pypa/pipenv#598, it's recommended to include Pipfile.lock in version control
Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it's recommended to include poetry.lock in version control
poetry.lock

# pdm
#   Similar to Pipfile.lock, it's recommended to include pdm.lock in version control
pdm.lock
__pypackages__/
.pdm-python/
pdm-build/

# pixi
pixi.lock
.pixi/

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/

# Ruff
.ruff_cache/

# PyPI configuration
.pypirc

# Marimo
marimo/_static/
marimo/lsp/
.marimo/

# Streamlit
.streamlit/secrets.toml

# MLflow
mlruns/

# R environment
renv/

# Application specific
wally_feedback_logs/
*.txt